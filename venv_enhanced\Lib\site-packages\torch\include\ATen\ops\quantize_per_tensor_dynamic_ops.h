#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API quantize_per_tensor_dynamic {
  using schema = at::Tensor (const at::Tensor &, at::ScalarType, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::quantize_per_tensor_dynamic";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "quantize_per_tensor_dynamic(Tensor self, ScalarType dtype, bool reduce_range) -> Tensor";
  static at::Tensor call(const at::Tensor & self, at::ScalarType dtype, bool reduce_range);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::ScalarType dtype, bool reduce_range);
};

struct TORCH_API quantize_per_tensor_dynamic_out {
  using schema = at::Tensor & (const at::Tensor &, at::ScalarType, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::quantize_per_tensor_dynamic";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "quantize_per_tensor_dynamic.out(Tensor self, ScalarType dtype, bool reduce_range, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, at::ScalarType dtype, bool reduce_range, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::ScalarType dtype, bool reduce_range, at::Tensor & out);
};

}} // namespace at::_ops
