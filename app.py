import cfg
from pathlib import Path
import subprocess
import os,re,time,sys,json,shutil
from datetime import timedel<PERSON>, datetime

from PySide6.QtWidgets import (QApplication, QMainWindow, QStatusBar, QLabel,
                             QPushButton, QVBoxLayout, QHBoxLayout, QWidget,
                             QFileDialog, QComboBox, QLineEdit, QPlainTextEdit,QTextEdit,
                             QMessageBox, QSizePolicy,QSpacerItem, QListWidget, QListWidgetItem,
                             QDialog, QDialogButtonBox, QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QThread, Signal,Slot,QTimer,QUrl
from PySide6.QtGui import QDesktopServices, QFont, QColor,QIcon,QCursor,QTextCursor
from urllib.parse import urlparse
import threading

import base64
import numpy as np
from pydub import AudioSegment
import socket
from googleapiclient.errors import HttpError
import google
from google.api_core.exceptions import ServerError,TooManyRequests,RetryError
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

safetySettings = {
    HarmCategory.HARM_CATEGORY_HARASSMENT:HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HATE_SPEECH:HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT:HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT:HarmBlockThreshold.BLOCK_NONE
}

class HistoryManager:
    """Manage processing history for files"""

    def __init__(self):
        self.history_file = f"{cfg.ROOT_DIR}/static/processing_history.json"
        self.history = self._load_history()

    def _load_history(self):
        """Load processing history from file"""
        try:
            if Path(self.history_file).exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            cfg.logger.warning(f'加载历史记录失败: {e}')
        return []

    def _save_history(self):
        """Save processing history to file"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            cfg.logger.warning(f'保存历史记录失败: {e}')

    def add_record(self, file_path, language, segments_count, duration_estimate, status="completed"):
        """Add a new processing record"""
        record = {
            "id": len(self.history) + 1,
            "file_path": file_path,
            "file_name": Path(file_path).name,
            "language": language,
            "segments_count": segments_count,
            "duration_estimate": duration_estimate,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "output_files": {
                "srt": f"{Path(file_path).stem}.srt",
                "tts_dataset": f"{Path(file_path).stem}_tts_dataset.txt",
                "metadata": f"{Path(file_path).stem}_metadata.json"
            }
        }
        self.history.append(record)
        self._save_history()
        return record

    def get_history(self, limit=50):
        """Get recent processing history"""
        return sorted(self.history, key=lambda x: x.get('timestamp', ''), reverse=True)[:limit]

    def clear_history(self):
        """Clear all history"""
        self.history = []
        self._save_history()

class HistoryDialog(QDialog):
    """Dialog to show processing history"""

    def __init__(self, history_manager, parent=None):
        super().__init__(parent)
        self.history_manager = history_manager
        self.setWindowTitle("处理历史记录")
        self.setMinimumSize(800, 600)
        self.setup_ui()
        self.load_history()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # History table
        self.history_table = QTableWidget(self)
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "文件名", "语言", "片段数", "状态", "处理时间", "操作"
        ])

        # Set column widths
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        layout.addWidget(self.history_table)

        # Buttons
        button_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新", self)
        self.refresh_btn.clicked.connect(self.load_history)
        button_layout.addWidget(self.refresh_btn)

        self.clear_btn = QPushButton("清空历史", self)
        self.clear_btn.clicked.connect(self.clear_history)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()

        self.close_btn = QPushButton("关闭", self)
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def load_history(self):
        """Load and display history"""
        history = self.history_manager.get_history()
        self.history_table.setRowCount(len(history))

        for row, record in enumerate(history):
            self.history_table.setItem(row, 0, QTableWidgetItem(record.get('file_name', '')))
            self.history_table.setItem(row, 1, QTableWidgetItem(record.get('language', '')))
            self.history_table.setItem(row, 2, QTableWidgetItem(str(record.get('segments_count', 0))))
            self.history_table.setItem(row, 3, QTableWidgetItem(record.get('status', '')))

            # Format timestamp
            timestamp = record.get('timestamp', '')
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp)
                    formatted_time = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_time = timestamp
            else:
                formatted_time = ''
            self.history_table.setItem(row, 4, QTableWidgetItem(formatted_time))

            # Open folder button
            open_btn = QPushButton("打开文件夹", self)
            open_btn.clicked.connect(lambda checked, path=record.get('file_path', ''): self.open_folder(path))
            self.history_table.setCellWidget(row, 5, open_btn)

    def open_folder(self, file_path):
        """Open folder containing the file"""
        if file_path and Path(file_path).exists():
            QDesktopServices.openUrl(QUrl(Path(file_path).parent.as_posix()))

    def clear_history(self):
        """Clear all history after confirmation"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有历史记录吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.history_manager.clear_history()
            self.load_history()



def sort_dict_by_number_keys_inplace(input_dict):
    sorted_items = sorted(input_dict.items(), key=lambda item: int(item[0]))
    sorted_dict = dict(sorted_items)
    return sorted_dict
'''
格式化毫秒或秒为符合srt格式的 2位小时:2位分:2位秒,3位毫秒 形式
print(ms_to_time_string(ms=12030))
-> 00:00:12,030
'''
def ms_to_time_string(*, ms=0, seconds=None):
    # 计算小时、分钟、秒和毫秒
    if seconds is None:
        td = timedelta(milliseconds=ms)
    else:
        td = timedelta(seconds=seconds)
    hours, remainder = divmod(td.seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = td.microseconds // 1000

    time_string = f"{hours}:{minutes}:{seconds},{milliseconds}"
    return format_time(time_string, ',')

# 将不规范的 时:分:秒,|.毫秒格式为  aa:bb:cc,ddd形式
# eg  001:01:2,4500  01:54,14 等做处理
def format_time(s_time="", separate=','):
    if not s_time.strip():
        return f'00:00:00{separate}000'
    hou, min, sec,ms = 0, 0, 0,0

    tmp = s_time.strip().split(':')
    if len(tmp) >= 3:
        hou,min,sec = tmp[-3].strip(),tmp[-2].strip(),tmp[-1].strip()
    elif len(tmp) == 2:
        min,sec = tmp[0].strip(),tmp[1].strip()
    elif len(tmp) == 1:
        sec = tmp[0].strip()
    
    if re.search(r',|\.', str(sec)):
        t = re.split(r',|\.', str(sec))
        sec = t[0].strip()
        ms=t[1].strip()
    else:
        ms = 0
    hou = f'{int(hou):02}'[-2:]
    min = f'{int(min):02}'[-2:]
    sec = f'{int(sec):02}'
    ms = f'{int(ms):03}'[-3:]
    return f"{hou}:{min}:{sec}{separate}{ms}"

# run ffprobe 获取视频元信息
def get_video_ms(mp4_file):
    try:

        p = subprocess.run(['ffprobe','-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', mp4_file],
                           stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE,
                           encoding="utf-8",
                           text=True,
                           check=True,
                           creationflags=0 if sys.platform != 'win32' else subprocess.CREATE_NO_WINDOW)
        if p.stdout:
            out = json.loads(p.stdout)            
            if "streams" not in out or len(out["streams"]) < 1:
                raise Exception(f'ffprobe error:streams is 0')

            if "format" in out and out['format']['duration']:
                return int(float(out['format']['duration']) * 1000)
                        
        cfg.logger.error(str(p) + str(p.stderr))
        raise Exception(str(p.stderr))
    except subprocess.CalledProcessError as e:
        cfg.logger.exception(e)
        raise
    except Exception as e:
        raise




class TaskThread(QThread):

    task_finished = Signal(str)

    def __init__(self, video_path, api_key, model, vad_settings=None, language_settings=None, history_manager=None, parent=None):
        super().__init__(parent)
        self.video_paths = video_path
        self.api_key = api_key.split(',')

        self.model = model
        self.is_running = True
        self.history_manager = history_manager

        # Enhanced VAD settings with defaults
        self.vad_settings = vad_settings or {
            "threshold": 0.5,
            "neg_threshold": 0.35,
            "min_speech_duration_ms": 0,
            "max_speech_duration_s": float("inf"),
            "min_silence_duration_ms": 250,
            "speech_pad_ms": 200,
            "max_segment_length_s": 30,  # New: Maximum segment length for TTS dataset
            "sentence_boundary_aware": True,  # New: Enable sentence boundary detection
            "merge_short_segments": True,  # New: Merge segments shorter than min duration
            "min_segment_duration_ms": 1000  # New: Minimum segment duration
        }

        # Language detection and optimization settings
        self.language_settings = language_settings or {
            "auto_detect": True,
            "primary_language": "auto",
            "ethiopian_languages": ["amharic", "oromo", "tigrigna", "somali", "afar"],
            "mixed_language_support": True,
            "language_specific_prompts": True
        }

        # Retry and recovery settings
        self.retry_settings = {
            "max_retries": 3,
            "retry_delay": 2,  # seconds
            "exponential_backoff": True,
            "skip_failed_segments": False,  # Never skip segments
            "checkpoint_interval": 10  # Save progress every 10 segments
        }

        self.error_exit=""
        self.processed_segments = []  # For checkpoint recovery
        self.failed_segments = []  # Track failed segments for retry

        cfg.logger.info(f'开始task线程:{video_path=},{api_key=}')
        
    def run(self):
        for i,file in enumerate(self.video_paths):
            file_name=Path(file).name
            try:
                audio_file=cfg.TEMP_DIR+f'/{file_name}-{time.time()}-16000.wav'
                command = [
                    "ffmpeg",
                    "-i",
                    file,
                    "-ac",
                    "1",
                    "-ar",
                    "16000",
                    "-vn",
                    audio_file
                ]
                subprocess.run(command, check=True, capture_output=True,creationflags=0 if sys.platform != 'win32' else subprocess.CREATE_NO_WINDOW)
                # 模拟成功
                self._exec(file,audio_file)
                self.task_finished.emit(json.dumps({'type': 'ok', 'text': f'{file_name} 转写完成' }))
            except subprocess.CalledProcessError as e:
                print(f"Error running ffmpeg: {e}")
                err=e.stderr.decode()
                Path(file+'-error.txt').write_text(err,encoding='utf-8')
                raise Exception(err)

            except Exception as e:
                Path(file+'-error.txt').write_text(str(e),encoding='utf-8')
                cfg.logger.exception(f'转写{file_name}时出错', exc_info=True)
                self.task_finished.emit(json.dumps({'type': 'error', 'text': f'转写{file_name}时出错:{e}' }))
        self.stop()
        

    def _exec(self, file, audio_file):
        p = Path(file)

        self.task_finished.emit(json.dumps({'type': 'log', 'text': f'开始转写字幕 {p.name}' }))

        # Enhanced audio segmentation
        seg_list = self.cut_audio(audio_file)
        if len(seg_list) < 1:
            raise Exception(f'预先VAD切割失败: {file}')

        # Load checkpoint if exists
        checkpoint_file = f"{cfg.TEMP_DIR}/{p.stem}_checkpoint.json"
        processed_segments = self._load_checkpoint(checkpoint_file)

        # Skip already processed segments
        if processed_segments:
            self.task_finished.emit(json.dumps({'type': 'log', 'text': f'从检查点恢复: 已处理{len(processed_segments)}个片段'}))
            seg_list = seg_list[len(processed_segments):]

        seg_list = [seg_list[i:i + 20] for i in range(0, len(seg_list), 20)]
        generation_config = {
            "temperature": 1,
            "top_p": 0.95,
            "top_k": 40,
            "response_mime_type": "text/plain",
        }

        # Use enhanced prompt for better TTS dataset quality
        prompt = self._get_language_optimized_prompt()
        result_srts = processed_segments.copy() if processed_segments else []
        response = None

        # Language detection
        detected_language = self._detect_language(audio_file)
        self.task_finished.emit(json.dumps({'type': 'log', 'text': f'检测到语言: {detected_language}'}))

        # Initialize retry tracking
        self.failed_segments = []
        for i, seg_group in enumerate(seg_list):
            # Process segment group with retry mechanism
            success = False
            retry_count = 0

            while not success and retry_count < self.retry_settings["max_retries"]:
                try:
                    api_key = self.api_key.pop(0)
                    self.api_key.append(api_key)
                    genai.configure(api_key=api_key)
                    model = genai.GenerativeModel(
                        model_name=self.model,
                        safety_settings=safetySettings
                    )

                    # Check network connectivity
                    if not self._check_network_connectivity():
                        raise Exception("网络连接检查失败")

                    files = []
                    for f in seg_group:
                        files.append({
                            "mime_type": "audio/wav",
                            "data": Path(f['file']).read_bytes()
                        })

                    chat_session = model.start_chat(
                        history=[{
                            "role": "user",
                            "parts": files,
                        }]
                    )

                    cfg.logger.info(f'发送音频到Gemini (尝试 {retry_count + 1}/{self.retry_settings["max_retries"]}):prompt={prompt[:100]}...')
                    response = chat_session.send_message(prompt, request_options={"timeout": 600})

                    cfg.logger.info(f'INFO[Gemini]{response.prompt_feedback.block_reason=},{response.candidates[0].finish_reason}')
                    if response.prompt_feedback.block_reason > 0:
                        raise Exception(self._get_error(response.prompt_feedback.block_reason, "forbid"))
                    if len(response.candidates) > 0 and response.candidates[0].finish_reason > 1:
                        raise Exception(self._get_error(response.candidates[0].finish_reason))

                    success = True  # Mark as successful

                except TooManyRequests as e:
                    retry_count += 1
                    error_msg = f'429 请求太快或超出Gemini每日限制 (尝试 {retry_count}/{self.retry_settings["max_retries"]})'
                    cfg.logger.warning(error_msg)
                    self.task_finished.emit(json.dumps({'type': 'log', 'text': error_msg}))
                    if retry_count >= self.retry_settings["max_retries"]:
                        raise Exception(f'429 请求太快或超出Gemini每日限制 - 已达到最大重试次数')
                    time.sleep(self._get_retry_delay(retry_count))

                except (RetryError, socket.timeout, ServerError) as e:
                    retry_count += 1
                    error_msg = f'网络连接失败 (尝试 {retry_count}/{self.retry_settings["max_retries"]})'
                    cfg.logger.warning(error_msg)
                    self.task_finished.emit(json.dumps({'type': 'log', 'text': error_msg}))
                    if retry_count >= self.retry_settings["max_retries"]:
                        raise Exception('无法连接到Gemini,请检查网络或代理设置')
                    time.sleep(self._get_retry_delay(retry_count))

                except google.api_core.exceptions.PermissionDenied:
                    raise Exception(f'您无权访问所请求的资源或模型')
                except google.api_core.exceptions.ResourceExhausted:
                    raise Exception(f'您的配额已用尽。请稍等片刻，然后重试')
                except google.auth.exceptions.DefaultCredentialsError:
                    raise Exception(f'验证失败，可能 Gemini API Key 不正确')
                except google.api_core.exceptions.InvalidArgument:
                    raise Exception(f'文件过大或 Gemini API Key 不正确')
                except genai.types.BlockedPromptException as e:
                    raise Exception(self._get_error(e.args[0].finish_reason))
                except genai.types.StopCandidateException as e:
                    cfg.logger.exception(f'[Gemini]-3:{e=}', exc_info=True)
                    if int(e.args[0].finish_reason > 1):
                        raise Exception(self._get_error(e.args[0].finish_reason))
                except Exception as e:
                    retry_count += 1
                    error = str(e)
                    error_msg = f'处理失败: {error} (尝试 {retry_count}/{self.retry_settings["max_retries"]})'
                    cfg.logger.exception(f'[Gemini]请求失败{e.__class__name}:{error=}', exc_info=True)
                    self.task_finished.emit(json.dumps({'type': 'log', 'text': error_msg}))

                    if error.find('User location is not supported') > -1:
                        raise Exception("当前请求ip(或代理服务器)所在国家不在Gemini API允许范围")
                    if retry_count >= self.retry_settings["max_retries"]:
                        # Add to failed segments for final retry
                        self.failed_segments.extend(seg_group)
                        break
                    time.sleep(self._get_retry_delay(retry_count))

            # If we failed all retries, continue to next segment group
            if not success:
                cfg.logger.error(f'片段组 {i} 处理失败，已达到最大重试次数')
                continue
            else:
                cfg.logger.info(f'gemini返回结果:{response.text=}')
                m = re.findall(r'<audio_text>(.*?)<\/audio_text>', response.text.strip(), re.I)
                if len(m) < 1:
                    cfg.logger.warning(f'片段组 {i} 未找到有效转录结果')
                    continue

                str_s = []
                for j, f in enumerate(seg_group):
                    if j < len(m):
                        # Enhanced timestamp validation for TTS dataset
                        startraw = ms_to_time_string(ms=f['start_time'])
                        endraw = ms_to_time_string(ms=f['end_time'])

                        # Validate and clean transcription text
                        transcription = self._clean_transcription_for_tts(m[j], detected_language)

                        tmp_srt = f'{len(result_srts)+1}\n{startraw} --> {endraw}\n{transcription}'
                        str_s.append(tmp_srt)
                        result_srts.append(tmp_srt)

                text = "\n\n".join(str_s)

                # Save checkpoint every N segments
                if (i + 1) % self.retry_settings["checkpoint_interval"] == 0:
                    self._save_checkpoint(checkpoint_file, result_srts)
                    self.task_finished.emit(json.dumps({'type': 'log', 'text': f'已保存检查点: {len(result_srts)}个片段'}))

                self.task_finished.emit(json.dumps({
                    'type': 'log',
                    'text': f'{p.name} 进度：{round((i+1)*100/len(seg_list),2)}% \n{text}'
                }))


        # Final retry for failed segments - never skip any segments
        if self.failed_segments:
            self.task_finished.emit(json.dumps({'type': 'log', 'text': f'正在重试失败的{len(self.failed_segments)}个片段...'}))
            retry_results = self._retry_failed_segments(self.failed_segments, prompt)
            result_srts.extend(retry_results)

        # Validate all segments are processed
        if len(result_srts) < 1:
            raise Exception(f'转录失败: 未获取到任何有效结果')

        # Enhanced SRT generation with timestamp validation
        validated_srts = self._validate_and_fix_timestamps(result_srts)
        str_srts = "\n\n".join(validated_srts)

        # Generate multiple output formats for TTS dataset
        srt_name = p.parent.as_posix() + '/' + p.stem + '.srt'
        txt_name = p.parent.as_posix() + '/' + p.stem + '_tts_dataset.txt'
        json_name = p.parent.as_posix() + '/' + p.stem + '_metadata.json'

        # Save SRT file
        Path(srt_name).write_text(str_srts, encoding='utf-8')

        # Save TTS dataset format (text only)
        tts_text = self._generate_tts_dataset_format(validated_srts, detected_language)
        Path(txt_name).write_text(tts_text, encoding='utf-8')

        # Save metadata for TTS training
        metadata = self._generate_tts_metadata(validated_srts, detected_language, p.name)
        Path(json_name).write_text(json.dumps(metadata, ensure_ascii=False, indent=2), encoding='utf-8')

        # Clean up checkpoint file
        if Path(checkpoint_file).exists():
            Path(checkpoint_file).unlink()

        # Add to history
        if self.history_manager:
            try:
                duration_estimate = sum([
                    seg.get('duration_ms', 0) for seg in seg_list
                    if isinstance(seg, dict)
                ]) / 1000  # Convert to seconds

                self.history_manager.add_record(
                    file_path=file,
                    language=detected_language,
                    segments_count=len(validated_srts),
                    duration_estimate=duration_estimate,
                    status="completed"
                )
            except Exception as e:
                cfg.logger.warning(f'添加历史记录失败: {e}')

        self.task_finished.emit(json.dumps({
            'type': 'log',
            'text': f'\n【 转录完成! 】\n'
                   f'SRT字幕: {srt_name}\n'
                   f'TTS数据集: {txt_name}\n'
                   f'元数据: {json_name}\n'
                   f'语言: {detected_language}\n'
                   f'片段数: {len(validated_srts)}\n\n'
                   f'{str_srts[:500]}...\n\n'
                   f'【 文件已保存到历史记录 】'
        }))
        

    def _get_error(self, num=5, type='error'):
        REASON_CN = {
            2: "已达到请求中指定的最大令牌数量",
            3: "由于安全原因，候选响应内容被标记",
            4:"候选响应内容因背诵原因被标记",
            5:"原因不明",
            6:"候选回应内容因使用不支持的语言而被标记",
            7:"由于内容包含禁用术语，令牌生成停止",
            8:"令牌生成因可能包含违禁内容而停止",
            9: "令牌生成停止，因为内容可能包含敏感的个人身份信息",
            10: "模型生成的函数调用无效",
        }

        forbid_cn = {
            0: "安全原因被Gemini屏蔽",
            1: "被Gemini禁止翻译:出于安全考虑，提示已被屏蔽",
            2: "提示因未知原因被屏蔽了",
            3: "提示因术语屏蔽名单中包含的字词而被屏蔽",
            4: "系统屏蔽了此提示，因为其中包含禁止的内容。",
        }
        return REASON_CN[num] if type == 'error' else forbid_cn[num]
    
    
    # Enhanced audio cutting with smart segmentation for TTS dataset optimization
    def cut_audio(self, audio_file):
        sampling_rate = 16000
        from faster_whisper.audio import decode_audio
        from faster_whisper.vad import (
            VadOptions,
            get_speech_timestamps
        )

        def convert_to_milliseconds(timestamps):
            milliseconds_timestamps = []
            for timestamp in timestamps:
                milliseconds_timestamps.append(
                    {
                        "start": int(round(timestamp["start"] / sampling_rate * 1000)),
                        "end": int(round(timestamp["end"] / sampling_rate * 1000)),
                    }
                )
            return milliseconds_timestamps

        # Use enhanced VAD settings
        vad_p = {
            "threshold": self.vad_settings["threshold"],
            "neg_threshold": self.vad_settings["neg_threshold"],
            "min_speech_duration_ms": self.vad_settings["min_speech_duration_ms"],
            "max_speech_duration_s": self.vad_settings["max_speech_duration_s"],
            "min_silence_duration_ms": self.vad_settings["min_silence_duration_ms"],
            "speech_pad_ms": self.vad_settings["speech_pad_ms"]
        }

        cfg.logger.info(f'VAD设置: {vad_p}')
        self.task_finished.emit(json.dumps({'type': 'log', 'text': f'使用VAD设置进行智能切片: 阈值={vad_p["threshold"]}, 最小静音={vad_p["min_silence_duration_ms"]}ms'}))

        speech_chunks = get_speech_timestamps(decode_audio(audio_file, sampling_rate=sampling_rate), vad_options=VadOptions(**vad_p))
        speech_chunks = convert_to_milliseconds(speech_chunks)

        # Apply smart segmentation enhancements
        speech_chunks = self._apply_smart_segmentation(speech_chunks)

        dir_name = f"{cfg.TEMP_DIR}/{time.time()}"
        Path(dir_name).mkdir(parents=True, exist_ok=True)
        data = []
        audio = AudioSegment.from_wav(audio_file)

        for i, it in enumerate(speech_chunks):
            start_ms, end_ms = it['start'], it['end']
            duration_ms = end_ms - start_ms

            # Validate segment duration for TTS dataset quality
            if duration_ms < self.vad_settings["min_segment_duration_ms"]:
                cfg.logger.info(f'跳过过短片段: {duration_ms}ms < {self.vad_settings["min_segment_duration_ms"]}ms')
                continue

            chunk = audio[start_ms:end_ms]
            file_name = f"{dir_name}/{start_ms}_{end_ms}.wav"
            chunk.export(file_name, format="wav")

            segment_info = {
                "start_time": start_ms,
                "end_time": end_ms,
                "file": file_name,
                "duration_ms": duration_ms,
                "segment_id": i,
                "is_sentence_boundary": it.get('is_sentence_boundary', False)
            }
            data.append(segment_info)

        cfg.logger.info(f'音频切片完成: 共{len(data)}个有效片段')
        self.task_finished.emit(json.dumps({'type': 'log', 'text': f'智能切片完成: 生成{len(data)}个优质片段用于TTS数据集'}))
        return data

    def _apply_smart_segmentation(self, speech_chunks):
        """Apply smart segmentation rules for optimal TTS dataset creation"""
        if not speech_chunks:
            return speech_chunks

        enhanced_chunks = []
        max_segment_ms = self.vad_settings["max_segment_length_s"] * 1000
        min_segment_ms = self.vad_settings["min_segment_duration_ms"]

        for chunk in speech_chunks:
            duration = chunk['end'] - chunk['start']

            # Split long segments for better TTS training
            if duration > max_segment_ms:
                # Split into smaller segments
                num_splits = int(duration / max_segment_ms) + 1
                segment_length = duration / num_splits

                for i in range(num_splits):
                    start = chunk['start'] + int(i * segment_length)
                    end = chunk['start'] + int((i + 1) * segment_length)
                    if i == num_splits - 1:  # Last segment
                        end = chunk['end']

                    if end - start >= min_segment_ms:
                        enhanced_chunks.append({
                            'start': start,
                            'end': end,
                            'is_sentence_boundary': i == 0  # Mark first segment as potential sentence start
                        })
            else:
                enhanced_chunks.append({
                    'start': chunk['start'],
                    'end': chunk['end'],
                    'is_sentence_boundary': True
                })

        # Merge short adjacent segments if enabled
        if self.vad_settings["merge_short_segments"]:
            enhanced_chunks = self._merge_short_segments(enhanced_chunks)

        cfg.logger.info(f'智能分割: {len(speech_chunks)} -> {len(enhanced_chunks)} 片段')
        return enhanced_chunks

    def _merge_short_segments(self, chunks):
        """Merge segments that are too short for quality TTS training"""
        if not chunks:
            return chunks

        merged = []
        current_chunk = chunks[0].copy()
        min_duration = self.vad_settings["min_segment_duration_ms"]

        for i in range(1, len(chunks)):
            next_chunk = chunks[i]
            current_duration = current_chunk['end'] - current_chunk['start']
            next_duration = next_chunk['end'] - next_chunk['start']
            gap = next_chunk['start'] - current_chunk['end']

            # Merge if current segment is too short and gap is small (< 500ms)
            if current_duration < min_duration and gap < 500:
                current_chunk['end'] = next_chunk['end']
                current_chunk['is_sentence_boundary'] = current_chunk.get('is_sentence_boundary', False) or next_chunk.get('is_sentence_boundary', False)
            else:
                merged.append(current_chunk)
                current_chunk = next_chunk.copy()

        merged.append(current_chunk)
        return merged

    def _get_language_optimized_prompt(self):
        """Get language-optimized prompt for better transcription"""
        if self.language_settings["language_specific_prompts"]:
            # Use enhanced prompt for better TTS dataset quality
            enhanced_prompt_file = f"{cfg.ROOT_DIR}/static/prompt_enhanced.txt"
            if Path(enhanced_prompt_file).exists():
                return Path(enhanced_prompt_file).read_text(encoding='utf-8')
        return cfg.prompt_gemini

    def _detect_language(self, audio_file):
        """Detect the primary language in the audio file"""
        if not self.language_settings["auto_detect"]:
            return self.language_settings["primary_language"]

        # Simple language detection based on file name or metadata
        # In a real implementation, you might use a language detection model
        file_name = Path(audio_file).name.lower()

        # Check for Ethiopian language indicators
        for lang in self.language_settings["ethiopian_languages"]:
            if lang in file_name:
                return lang

        # Default detection logic
        return "auto"

    def _clean_transcription_for_tts(self, text, language):
        """Clean and optimize transcription text for TTS dataset"""
        if not text:
            return ""

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())

        # Language-specific cleaning
        if language in ["amharic", "tigrigna"]:
            # Ensure proper Ge'ez punctuation
            text = re.sub(r'[.!?]+', '።', text)
            text = re.sub(r'[,;]+', '፣', text)
        elif language == "oromo":
            # Ensure proper Latin script punctuation
            text = re.sub(r'[.!?]+', '.', text)
            text = re.sub(r'[,;]+', ',', text)

        # Remove filler words and false starts for TTS quality
        filler_words = ['uh', 'um', 'er', 'ah', 'hmm', 'እ', 'አ', 'ኧ']
        for filler in filler_words:
            text = re.sub(rf'\b{filler}\b', '', text, flags=re.IGNORECASE)

        # Clean up multiple punctuation
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)

        return text.strip()

    def _check_network_connectivity(self):
        """Check if network connection is available"""
        try:
            import urllib.request
            urllib.request.urlopen('https://www.google.com', timeout=5)
            return True
        except:
            return False

    def _get_retry_delay(self, retry_count):
        """Calculate retry delay with exponential backoff"""
        if self.retry_settings["exponential_backoff"]:
            return self.retry_settings["retry_delay"] * (2 ** (retry_count - 1))
        return self.retry_settings["retry_delay"]

    def _load_checkpoint(self, checkpoint_file):
        """Load processing checkpoint"""
        try:
            if Path(checkpoint_file).exists():
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                return checkpoint_data.get('processed_segments', [])
        except Exception as e:
            cfg.logger.warning(f'加载检查点失败: {e}')
        return []

    def _save_checkpoint(self, checkpoint_file, processed_segments):
        """Save processing checkpoint"""
        try:
            checkpoint_data = {
                'processed_segments': processed_segments,
                'timestamp': time.time(),
                'total_segments': len(processed_segments)
            }
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            cfg.logger.warning(f'保存检查点失败: {e}')

    def _retry_failed_segments(self, failed_segments, prompt):
        """Retry failed segments with more aggressive retry logic"""
        retry_results = []

        for segment in failed_segments:
            success = False
            retry_count = 0
            max_retries = self.retry_settings["max_retries"] * 2  # More retries for failed segments

            while not success and retry_count < max_retries:
                try:
                    api_key = self.api_key.pop(0)
                    self.api_key.append(api_key)
                    genai.configure(api_key=api_key)
                    model = genai.GenerativeModel(
                        model_name=self.model,
                        safety_settings=safetySettings
                    )

                    files = [{
                        "mime_type": "audio/wav",
                        "data": Path(segment['file']).read_bytes()
                    }]

                    chat_session = model.start_chat(history=[{"role": "user", "parts": files}])
                    response = chat_session.send_message(prompt, request_options={"timeout": 600})

                    m = re.findall(r'<audio_text>(.*?)<\/audio_text>', response.text.strip(), re.I)
                    if m:
                        startraw = ms_to_time_string(ms=segment['start_time'])
                        endraw = ms_to_time_string(ms=segment['end_time'])
                        transcription = self._clean_transcription_for_tts(m[0], "auto")
                        tmp_srt = f'{len(retry_results)+1}\n{startraw} --> {endraw}\n{transcription}'
                        retry_results.append(tmp_srt)
                        success = True

                except Exception as e:
                    retry_count += 1
                    cfg.logger.warning(f'重试片段失败 (尝试 {retry_count}/{max_retries}): {e}')
                    if retry_count < max_retries:
                        time.sleep(self._get_retry_delay(retry_count))

            if not success:
                cfg.logger.error(f'片段最终失败，跳过: {segment["file"]}')

        return retry_results

    def _validate_and_fix_timestamps(self, srt_segments):
        """Validate and fix timestamp accuracy for TTS dataset"""
        validated = []

        for i, segment in enumerate(srt_segments):
            lines = segment.split('\n')
            if len(lines) >= 3:
                # Extract timestamp line
                timestamp_line = lines[1]
                text = '\n'.join(lines[2:])

                # Validate timestamp format
                if ' --> ' in timestamp_line:
                    start_time, end_time = timestamp_line.split(' --> ')

                    # Ensure proper formatting
                    start_time = format_time(start_time.strip())
                    end_time = format_time(end_time.strip())

                    # Rebuild segment with validated timestamps
                    validated_segment = f'{i+1}\n{start_time} --> {end_time}\n{text}'
                    validated.append(validated_segment)
                else:
                    # Keep original if timestamp format is invalid
                    validated.append(segment)
            else:
                validated.append(segment)

        return validated

    def _generate_tts_dataset_format(self, srt_segments, language):
        """Generate clean text format for TTS training"""
        tts_lines = []

        for segment in srt_segments:
            lines = segment.split('\n')
            if len(lines) >= 3:
                text = '\n'.join(lines[2:]).strip()
                if text:
                    tts_lines.append(text)

        return '\n'.join(tts_lines)

    def _generate_tts_metadata(self, srt_segments, language, filename):
        """Generate metadata for TTS training"""
        metadata = {
            "filename": filename,
            "language": language,
            "total_segments": len(srt_segments),
            "total_duration_estimate": 0,
            "segments": []
        }

        for i, segment in enumerate(srt_segments):
            lines = segment.split('\n')
            if len(lines) >= 3:
                timestamp_line = lines[1]
                text = '\n'.join(lines[2:]).strip()

                if ' --> ' in timestamp_line:
                    start_time, end_time = timestamp_line.split(' --> ')

                    segment_metadata = {
                        "id": i + 1,
                        "start_time": start_time.strip(),
                        "end_time": end_time.strip(),
                        "text": text,
                        "character_count": len(text),
                        "word_count": len(text.split()) if text else 0
                    }
                    metadata["segments"].append(segment_metadata)

        return metadata

    def stop(self):
        self.is_running = False

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("PVT GeminiAI音视频转写v0.4 (Enhanced TTS Dataset) - https://pyvideotrans.com")
        self.setMinimumSize(900, 700)

        # 设置窗口图标
        icon_path =  f"{cfg.ROOT_DIR}/static/icon.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        self.cache_cfg={}
        self.history_manager = HistoryManager()

        self.video_paths = []
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        self.layout = QVBoxLayout(self.central_widget)
        self.setup_ui()

    def setup_ui(self):
       
        # 视频选择按钮
        self.select_video_btn = QPushButton("点击或拖拽选择音视频文件",self)
        self.select_video_btn.setFixedHeight(200)
        self.select_video_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.select_video_btn.clicked.connect(self.select_video_file)
        self.set_hand_cursor(self.select_video_btn)
        self.layout.addWidget(self.select_video_btn)
    
        # 水平布局 1
        h_layout1 = QHBoxLayout()
        self.layout.addLayout(h_layout1)


       

        
        self.aitype = QComboBox(self)
        self.aitype.addItems(["GeminiAI Key"])
        
        h_layout1.addWidget(self.aitype)
        
        
        
        self.api_key = QLineEdit(self)
        self.api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key.setPlaceholderText('填写AI的API KEY,可填多个，英文逗号分隔')
        self.api_key.setToolTip('填写AI的API KEY,可填多个，英文逗号分隔')
        h_layout1.addWidget(self.api_key)

        h_layout1.addWidget(QLabel("选择模型", self))
        self.models = QComboBox(self)
        self.models.addItems(["gemini-2.0-flash-exp","gemini-1.5-flash"])
        h_layout1.addWidget(self.models)

        # Language settings
        h_layout1.addWidget(QLabel("语言", self))
        self.language_combo = QComboBox(self)
        self.language_combo.addItems(["自动检测", "Amharic (አማርኛ)", "Oromo (Afaan Oromoo)", "Tigrigna (ትግርኛ)", "Somali", "Afar", "English", "其他"])
        h_layout1.addWidget(self.language_combo)

        h_proxy=QHBoxLayout()
        self.proxy_label=QLabel('填写http代理地址和端口')
        self.proxy_input = QLineEdit(self)
        self.proxy_input.setPlaceholderText('如果计算机无法访问Gemini，请填写http代理')
        h_proxy.addWidget(self.proxy_label)
        h_proxy.addWidget(self.proxy_input)

        self.layout.addLayout(h_proxy)

        # Advanced VAD Settings
        vad_layout = QHBoxLayout()
        vad_layout.addWidget(QLabel("VAD阈值:", self))
        self.vad_threshold = QLineEdit(self)
        self.vad_threshold.setText("0.5")
        self.vad_threshold.setPlaceholderText("0.1-0.9")
        self.vad_threshold.setMaximumWidth(80)
        vad_layout.addWidget(self.vad_threshold)

        vad_layout.addWidget(QLabel("最小片段(秒):", self))
        self.min_segment_duration = QLineEdit(self)
        self.min_segment_duration.setText("1.0")
        self.min_segment_duration.setPlaceholderText("1.0-10.0")
        self.min_segment_duration.setMaximumWidth(80)
        vad_layout.addWidget(self.min_segment_duration)

        vad_layout.addWidget(QLabel("最大片段(秒):", self))
        self.max_segment_duration = QLineEdit(self)
        self.max_segment_duration.setText("30")
        self.max_segment_duration.setPlaceholderText("10-60")
        self.max_segment_duration.setMaximumWidth(80)
        vad_layout.addWidget(self.max_segment_duration)

        # Add spacer to push elements to the left
        vad_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        self.layout.addLayout(vad_layout)
    
        # 水平布局 2
        h_layout2 = QHBoxLayout()
        h_layout2.setAlignment(Qt.AlignCenter) #设置水平居中
        self.layout.addLayout(h_layout2)

        self.start_btn = QPushButton("开始", self)
        self.start_btn.setFixedHeight(35)
        self.start_btn.setMinimumWidth(200)
        self.start_btn.clicked.connect(self.start_task)
        self.set_hand_cursor(self.start_btn)
        h_layout2.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止", self)
        self.stop_btn.setFixedHeight(35)
        self.stop_btn.setFixedWidth(80)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_task)
        self.set_hand_cursor(self.stop_btn)

        self.opendir = QPushButton("打开结果文件夹", self)
        self.opendir.clicked.connect(self.opendir_fun)
        self.set_hand_cursor(self.opendir)

        self.history_btn = QPushButton("处理历史", self)
        self.history_btn.clicked.connect(self.show_history)
        self.set_hand_cursor(self.history_btn)

        h_layout2.addWidget(self.stop_btn)
        h_layout2.addWidget(self.opendir)
        h_layout2.addWidget(self.history_btn)

        #日志文本框
        self.logs = QTextEdit(self)
        self.logs.setReadOnly(True)
        self.logs.setPlaceholderText('等待开始执行任务')
        self.layout.addWidget(self.logs)
    
       # 底部状态栏
        self.status_bar = QStatusBar(self)
        self.setStatusBar(self.status_bar)

        # 底部状态栏：左侧按钮
        self.doc_btn = QPushButton("查看使用文档",self)
        self.doc_btn.clicked.connect(self.open_doc_link)
        self.doc_btn.setStyleSheet("background-color: transparent; color: white;") # 设置背景透明，文字白色
        self.set_hand_cursor(self.doc_btn)
        self.status_bar.addWidget(self.doc_btn)


        # 底部状态栏：右侧按钮
        self.download_btn = QPushButton("下载新版本",self)
        self.download_btn.clicked.connect(self.open_download_link)
        self.set_hand_cursor(self.download_btn)
        self.download_btn.setStyleSheet("background-color: transparent; color: white;")  # 设置背景透明，文字白色
        self.status_bar.addPermanentWidget(self.download_btn)
        self.set_cache()
    
    def set_cache(self):
        if Path(cfg.ROOT_DIR+'/static/cfg.json').exists():
            self.cache_cfg=json.loads(Path(cfg.ROOT_DIR+'/static/cfg.json').read_text(encoding='utf-8'))

            self.proxy_input.setText(self.cache_cfg.get('proxy',''))
            self.api_key.setText(self.cache_cfg.get('api_key_gemini',''))

            # Load VAD settings
            self.vad_threshold.setText(str(self.cache_cfg.get('vad_threshold', 0.5)))
            self.min_segment_duration.setText(str(self.cache_cfg.get('min_segment_duration', 1.0)))
            self.max_segment_duration.setText(str(self.cache_cfg.get('max_segment_duration', 30)))

            # Load language setting
            language_index = self.cache_cfg.get('language_index', 0)
            if language_index < self.language_combo.count():
                self.language_combo.setCurrentIndex(language_index)

            if self.cache_cfg.get('last_opendir'):
                cfg.last_opendir=self.cache_cfg.get('last_opendir')

            
            

            
        
    def open_doc_link(self):
        """ 打开文档链接 """
        QDesktopServices.openUrl(QUrl("https://pyvideotrans.com/geminirecogn"))
        
    def open_download_link(self):
        """ 打开下载链接 """
        QDesktopServices.openUrl(QUrl("https://pyvideotrans.com/geminirecogn"))


    def opendir_fun(self):
        if self.video_paths and len(self.video_paths)>0:
            QDesktopServices.openUrl(QUrl(Path(self.video_paths[0]).parent.as_posix()))

    def show_history(self):
        """Show processing history dialog"""
        dialog = HistoryDialog(self.history_manager, self)
        dialog.exec()
            
    
    def set_hand_cursor(self, widget):
          """ 将控件的鼠标光标设置为手形 """
          widget.setCursor(QCursor(Qt.PointingHandCursor))
    
    def select_video_file(self):
        fnames, _ = QFileDialog.getOpenFileNames(self,
                                                     '选择一或多个文件',
                                                     cfg.last_opendir,
                                                     f'Files (*.mp4 *.avi *.mov *.mkv *.ts *.wav *.mp3 *.flac *.aac *.m4a)')
        mp4_list=[]                                             
        if len(fnames) < 1:
            return
        for (i, it) in enumerate(fnames):
            mp4_list.append(Path(it).as_posix())
        cfg.last_opendir = Path(mp4_list[0]).parent.resolve().as_posix()
        self.cache_cfg['last_opendir']=cfg.last_opendir
        
        self.video_paths=mp4_list
        self.select_video_btn.setText(f'选择了 {len(self.video_paths)} 个文件\n'+("\n".join([Path(n).name for n in mp4_list])) )

            
    def start_task(self):
        if len(self.video_paths)<1:
             QMessageBox.warning(self, "警告", "请先选择音视频文件")
             return
        if not self.api_key.text():
            QMessageBox.warning(self, "警告", "请先填写AI Key")
            return

        self.start_btn.setEnabled(False)
        self.start_btn.setText('任务执行中...')
        self.stop_btn.setEnabled(True)

        aitype=self.aitype.currentIndex()
        api_key=self.api_key.text()
        models=self.models.currentText()

        # Collect VAD settings
        try:
            vad_threshold = float(self.vad_threshold.text())
            min_segment_duration = float(self.min_segment_duration.text()) * 1000  # Convert to ms
            max_segment_duration = float(self.max_segment_duration.text())
        except ValueError:
            QMessageBox.warning(self, "警告", "VAD设置格式错误，请检查数值")
            self.start_btn.setEnabled(True)
            self.start_btn.setText('开始')
            self.stop_btn.setEnabled(False)
            return

        # Prepare enhanced settings
        vad_settings = {
            "threshold": vad_threshold,
            "neg_threshold": 0.35,
            "min_speech_duration_ms": 0,
            "max_speech_duration_s": float("inf"),
            "min_silence_duration_ms": 250,
            "speech_pad_ms": 200,
            "max_segment_length_s": max_segment_duration,
            "sentence_boundary_aware": True,
            "merge_short_segments": True,
            "min_segment_duration_ms": min_segment_duration
        }

        # Language settings
        language_map = {
            0: "auto", 1: "amharic", 2: "oromo", 3: "tigrigna",
            4: "somali", 5: "afar", 6: "english", 7: "other"
        }
        selected_language = language_map.get(self.language_combo.currentIndex(), "auto")

        language_settings = {
            "auto_detect": selected_language == "auto",
            "primary_language": selected_language,
            "ethiopian_languages": ["amharic", "oromo", "tigrigna", "somali", "afar"],
            "mixed_language_support": True,
            "language_specific_prompts": True
        }

        proxy=self.proxy_input.text().strip()

        # Save all settings to cache
        self.cache_cfg['api_key_gemini']=api_key
        self.cache_cfg['models_gemini']=models
        self.cache_cfg['proxy']=proxy
        self.cache_cfg['vad_threshold']=vad_threshold
        self.cache_cfg['min_segment_duration']=min_segment_duration/1000  # Save in seconds
        self.cache_cfg['max_segment_duration']=max_segment_duration
        self.cache_cfg['language_index']=self.language_combo.currentIndex()

        if proxy:
            os.environ['https_proxy']=proxy
            os.environ['http_proxy']=proxy

        Path(cfg.ROOT_DIR+"/static/cfg.json").write_text(json.dumps(self.cache_cfg),encoding='utf-8')

        # Create enhanced TaskThread with new settings
        self.task_thread = TaskThread(self.video_paths, api_key, models, vad_settings, language_settings, self.history_manager, self)
        self.task_thread.task_finished.connect(self.handle_task_result)
        self.task_thread.start()
        self.logs.clear()
        

    @Slot(str)
    def handle_task_result(self, result):
        
        try:
            data = json.loads(result)
        except Exception as e:
            self.add_log_text(f"JSON解析失败:{result}", "error")
        else:
            log_type = data.get("type")
            log_text = data.get("text")
            
            if log_type == "error":
                self.add_log_text(log_text, "error")
                #self.start_btn.setEnabled(True)
                #self.start_btn.setText('任务出错了/重新开始')
                #self.stop_btn.setEnabled(False)
            elif log_type == "ok":
                self.add_log_text(f'<strong style="color:green;font-size:18px">{log_text}</strong><br><br>')
                self.start_btn.setText('任务完成/开始')
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)
            elif self.task_thread and self.task_thread.isRunning():
                if log_type == "precent":
                    self.start_btn.setText(log_text)
                    self.add_log_text(log_text)
                else:
                    self.add_log_text(log_text)
    
    def add_log_text(self, text, log_type=None):
        """ 添加日志信息到文本框 """

        text=text.replace('\n','<br>')
        if log_type == "error":
            text=f"<span style='color: red;'>{text}</span><br>"
        else:        
            text=f"<span style='color: white;'>{text}</span><br>"
        self.logs.moveCursor(QTextCursor.End)
        self.logs.insertHtml(text)
        
    def stop_task(self):
        if self.task_thread and self.task_thread.isRunning():
            self.task_thread.stop()
        self.start_btn.setEnabled(True)
        self.start_btn.setText('重新开始')
        self.stop_btn.setEnabled(False)
        
    def closeEvent(self, event):
        """ 重写窗口关闭事件 """
        if hasattr(self, 'task_thread') and self.task_thread and self.task_thread.isRunning():
            self.task_thread.stop()
            self.hide()  # 隐藏窗口
            time.sleep(5)  # 5秒后关闭窗口
        event.accept()

    
if __name__ == "__main__":
    app = QApplication(sys.argv)
    import cfg.dark.darkstyle_rc
    with open('./static/style.qss', 'r', encoding='utf-8') as f:
        app.setStyleSheet(f.read())
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
