# Enhanced Gemini AI Speech-to-SRT Application Usage Guide

## 🚀 Quick Start

### Installation Complete ✅
The application has been successfully installed using UV in a separate virtual environment (`venv_enhanced`).

### Running the Application

**Option 1: Using Batch File (Windows)**
```bash
run_enhanced_app.bat
```

**Option 2: Using PowerShell Script**
```powershell
.\run_enhanced_app.ps1
```

**Option 3: Manual Activation**
```bash
# Activate virtual environment
.\venv_enhanced\Scripts\activate

# Run application
python app.py
```

## 🎯 Enhanced Features Guide

### 1. Smart VAD Segmentation
- **VAD Threshold**: Adjust sensitivity (0.1-0.9)
  - Lower values = more sensitive (captures quieter speech)
  - Higher values = less sensitive (ignores background noise)
- **Min Segment Duration**: 1-10 seconds (optimal for TTS: 2-5 seconds)
- **Max Segment Duration**: 10-60 seconds (optimal for TTS: 15-30 seconds)

### 2. Language Selection
Choose from the dropdown:
- **自动检测** (Auto-detect)
- **Amharic (አማርኛ)** - Ge'ez script with proper punctuation
- **<PERSON><PERSON> (Afaan <PERSON>)** - Latin script optimization
- **Tigrigna (ትግርኛ)** - Ge'ez script support
- **Somali** - Latin script
- **Afar** - Latin script
- **English** - Standard processing
- **其他** (Other languages)

### 3. Processing Workflow

#### Step 1: Configure Settings
1. **API Key**: Enter your Gemini AI API key(s) (comma-separated for multiple keys)
2. **Model**: Choose between `gemini-2.0-flash-exp` or `gemini-1.5-flash`
3. **Language**: Select target language or auto-detect
4. **VAD Settings**: Adjust threshold and segment durations
5. **Proxy**: Configure if needed for network access

#### Step 2: Select Files
- Click "点击或拖拽选择音视频文件" or drag & drop files
- Supports: MP4, AVI, MOV, MKV, TS, WAV, MP3, FLAC, AAC, M4A

#### Step 3: Start Processing
- Click "开始" to begin processing
- Monitor progress with real-time updates
- View VAD settings and language detection results

#### Step 4: Review Results
Multiple output files are generated:
- **`.srt`** - Standard subtitle file
- **`_tts_dataset.txt`** - Clean text for TTS training
- **`_metadata.json`** - Comprehensive metadata

### 4. Processing History
- Click "处理历史" to view all processed files
- Access previous results and output folders
- Track processing statistics and language detection

## 🔧 Advanced Features

### Automatic Retry System
- **3 retry attempts** for failed segments
- **Exponential backoff** for network issues
- **Never skips segments** - ensures complete transcription
- **Checkpoint recovery** - resume interrupted processing

### TTS Dataset Optimization
- **Optimal segment length** for TTS training
- **Clean transcriptions** with filler word removal
- **Language-specific punctuation** for Ethiopian languages
- **Timestamp validation** for accuracy
- **Quality metrics** in metadata

### Error Handling
- **Network connectivity monitoring**
- **Detailed error reporting** with retry status
- **Checkpoint system** saves progress every 10 segments
- **Automatic recovery** from interruptions

## 📊 Output Files Explained

### SRT File (`.srt`)
Standard subtitle format with accurate timestamps:
```
1
00:00:01,000 --> 00:00:05,500
የዚህ ፕሮግራም ዋና ዓላማ የድምጽ ፋይሎችን ወደ ጽሑፍ መቀየር ነው።

2
00:00:06,000 --> 00:00:10,200
This program converts audio files to text format.
```

### TTS Dataset File (`_tts_dataset.txt`)
Clean text optimized for TTS training:
```
የዚህ ፕሮግራም ዋና ዓላማ የድምጽ ፋይሎችን ወደ ጽሑፍ መቀየር ነው።
This program converts audio files to text format.
```

### Metadata File (`_metadata.json`)
Comprehensive information for dataset management:
```json
{
  "filename": "example.mp4",
  "language": "amharic",
  "total_segments": 25,
  "segments": [
    {
      "id": 1,
      "start_time": "00:00:01,000",
      "end_time": "00:00:05,500",
      "text": "የዚህ ፕሮግራም ዋና ዓላማ...",
      "character_count": 45,
      "word_count": 8
    }
  ]
}
```

## 🛠️ Troubleshooting

### Common Issues

**1. Network Connection Errors**
- Configure proxy settings if behind firewall
- Check internet connectivity
- Verify Gemini API access from your region

**2. API Key Issues**
- Ensure valid Gemini API key
- Check API quotas and limits
- Use multiple keys for higher throughput

**3. Audio Processing Errors**
- Verify FFmpeg is available
- Check audio file format compatibility
- Ensure sufficient disk space for temporary files

**4. VAD Segmentation Issues**
- Adjust VAD threshold for your audio quality
- Modify min/max segment durations
- Check for background noise levels

### Performance Tips

1. **Use multiple API keys** for faster processing
2. **Adjust VAD threshold** based on audio quality
3. **Set appropriate segment lengths** for your TTS model
4. **Monitor processing history** for optimization insights
5. **Use proxy** if experiencing network issues

## 📈 Best Practices for TTS Dataset Creation

1. **Audio Quality**: Use high-quality, clear audio recordings
2. **Segment Length**: 2-5 seconds optimal for most TTS models
3. **Language Consistency**: Process single-language files separately
4. **VAD Tuning**: Adjust threshold based on speaker and environment
5. **Review Results**: Check generated text for accuracy
6. **Metadata Usage**: Use JSON metadata for dataset organization

## 🎉 Success Indicators

✅ **Installation Complete**: All dependencies installed via UV
✅ **Enhanced Features Active**: Smart VAD, language support, retry system
✅ **TTS Optimization**: Multiple output formats generated
✅ **History Tracking**: Processing records maintained
✅ **Error Recovery**: Checkpoint system and retry mechanisms working

The enhanced application is now ready for professional TTS dataset creation with Ethiopian language support!
