# Enhanced Gemini AI Speech-to-SRT Application Launcher
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Enhanced Gemini AI Speech-to-SRT v0.4" -ForegroundColor Green
Write-Host "TTS Dataset Creation Tool (English UI)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& ".\venv_enhanced\Scripts\Activate.ps1"

Write-Host ""
Write-Host "Starting enhanced application with English interface..." -ForegroundColor Green
Write-Host ""
Write-Host "Features available:" -ForegroundColor Cyan
Write-Host "- Smart VAD segmentation" -ForegroundColor White
Write-Host "- Ethiopian languages support (Amharic, Oro<PERSON>, Tigrigna)" -ForegroundColor White
Write-Host "- Automatic retry mechanisms" -ForegroundColor White
Write-Host "- Processing history tracking" -ForegroundColor White
Write-Host "- TTS dataset optimization" -ForegroundColor White
Write-Host "- Complete English user interface" -ForegroundColor White
Write-Host ""

python app.py

Write-Host ""
Write-Host "Application closed." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
