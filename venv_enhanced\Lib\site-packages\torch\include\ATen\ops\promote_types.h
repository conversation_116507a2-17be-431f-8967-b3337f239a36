#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/promote_types_ops.h>

namespace at {


// aten::promote_types(ScalarType type1, ScalarType type2) -> ScalarType
inline at::ScalarType promote_types(at::ScalarType type1, at::ScalarType type2) {
    return at::_ops::promote_types::call(type1, type2);
}

}
