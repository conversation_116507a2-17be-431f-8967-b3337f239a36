#!/usr/bin/env python3
"""
Test script to verify all enhanced features are working correctly
"""

import sys
import json
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import cfg
        print("✅ cfg module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import cfg: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Google Generative AI: {e}")
        return False
    
    try:
        from faster_whisper.vad import VadOptions, get_speech_timestamps
        print("✅ Faster Whisper VAD imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Faster Whisper VAD: {e}")
        return False
    
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import PySide6: {e}")
        return False
    
    try:
        from pydub import AudioSegment
        print("✅ PyDub imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import PyDub: {e}")
        return False
    
    return True

def test_enhanced_features():
    """Test enhanced features"""
    print("\n🚀 Testing enhanced features...")
    
    # Test enhanced prompt file
    enhanced_prompt_file = Path("static/prompt_enhanced.txt")
    if enhanced_prompt_file.exists():
        print("✅ Enhanced prompt file exists")
        content = enhanced_prompt_file.read_text(encoding='utf-8')
        if "Ethiopian languages" in content and "TTS" in content:
            print("✅ Enhanced prompt contains Ethiopian language and TTS optimizations")
        else:
            print("❌ Enhanced prompt missing expected content")
    else:
        print("❌ Enhanced prompt file not found")
    
    # Test history manager
    try:
        from app import HistoryManager
        history_manager = HistoryManager()
        print("✅ HistoryManager class imported and instantiated successfully")
    except ImportError as e:
        print(f"❌ Failed to import HistoryManager: {e}")
    
    # Test TaskThread enhancements
    try:
        from app import TaskThread
        print("✅ Enhanced TaskThread class imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import TaskThread: {e}")
    
    return True

def test_configuration():
    """Test configuration files"""
    print("\n⚙️ Testing configuration...")
    
    # Test config file
    config_file = Path("static/cfg.json")
    if config_file.exists():
        try:
            config = json.loads(config_file.read_text(encoding='utf-8'))
            print("✅ Configuration file loaded successfully")
            print(f"   - API Key configured: {'api_key_gemini' in config}")
            print(f"   - Proxy configured: {'proxy' in config}")
            print(f"   - VAD settings: {'vad_threshold' in config}")
        except json.JSONDecodeError as e:
            print(f"❌ Configuration file has invalid JSON: {e}")
    else:
        print("⚠️ Configuration file not found (will be created on first run)")
    
    return True

def test_file_structure():
    """Test required file structure"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "app.py",
        "cfg/__init__.py", 
        "static/prompt.txt",
        "static/prompt_enhanced.txt",
        "static/style.qss",
        "requirements.txt"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing!")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Enhanced Gemini AI Speech-to-SRT Application Test Suite")
    print("=" * 60)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_imports()
    all_passed &= test_enhanced_features()
    all_passed &= test_configuration()
    all_passed &= test_file_structure()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests passed! The enhanced application is ready to run.")
        print("\n📋 Enhanced Features Available:")
        print("   • Smart VAD segmentation with configurable settings")
        print("   • Multi-language support (Ethiopian languages)")
        print("   • Automatic retry mechanisms with checkpoint recovery")
        print("   • Processing history tracking")
        print("   • TTS dataset optimization")
        print("   • Enhanced error handling and network monitoring")
        print("\n🚀 To run the application:")
        print("   python app.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
